#!/usr/bin/env python3
"""
分布式爬虫启动脚本
支持不同机器的不同调度策略
"""

import os
import sys
import time
import argparse
import schedule
import logging
from datetime import datetime
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from crawl.tools.distributed_queue_manager import get_queue_manager
from data_update_config import data_update_config, UpdateFrequency

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('distributed_crawler.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 环境变量配置 - 调度时间间隔（分钟）
# 优化后的默认值：降低更新频率，减少 Redis 存储压力
SCHEDULE_CONFIG = {
    'frequent_minutes': int(os.getenv('CRAWLER_FREQUENT_MINUTES', '30')),     # 频繁更新间隔（30分钟，原5分钟）
    'periodic_minutes': int(os.getenv('CRAWLER_PERIODIC_MINUTES', '360')),    # 周期更新间隔（6小时，原3小时）
    'maintenance_minutes': int(os.getenv('CRAWLER_MAINTENANCE_MINUTES', '120')), # 维护检查间隔（2小时，原1小时）
    'refresh_minutes': int(os.getenv('CRAWLER_REFRESH_MINUTES', '720')),      # 数据刷新间隔（12小时，原6小时）
    'smart_realtime_minutes': int(os.getenv('CRAWLER_SMART_REALTIME_MINUTES', '30')),   # 智能调度-实时（30分钟，原5分钟）
    'smart_minute_minutes': int(os.getenv('CRAWLER_SMART_MINUTE_MINUTES', '60')),       # 智能调度-分时（1小时，原15分钟）
    'smart_detail_minutes': int(os.getenv('CRAWLER_SMART_DETAIL_MINUTES', '240')),      # 智能调度-详情（4小时，原1小时）
}


class DistributedCrawler:
    """分布式爬虫调度器 - 优化版（仅数据更新）"""

    def __init__(self, machine_id: str, role: str = "worker"):
        self.machine_id = machine_id
        self.role = role  # "master" 或 "worker"
        self.queue_manager = get_queue_manager(machine_id)

        # 只有 master 节点可以执行数据更新
        self.can_update_data = (role == "master")

        logger.info(f"分布式爬虫初始化: 机器ID={machine_id}, 角色={role}")
        logger.info(f"数据更新权限: {'允许' if self.can_update_data else '禁止'}")

        if not self.can_update_data:
            logger.warning("⚠️  当前节点为 worker，不允许执行数据更新操作")

        # Redis 数据管理配置（基于实际去重分析结果）
        # 分析显示：39.5% 数据被去重，存储效率 60.5%
        self.redis_management = {
            'max_queue_size': int(os.getenv('CRAWLER_MAX_QUEUE_SIZE', '18000')),      # 基于实际去重效果调整
            'cleanup_threshold': int(os.getenv('CRAWLER_CLEANUP_THRESHOLD', '30000')), # 基于实际去重效果调整
            'keep_recent_count': int(os.getenv('CRAWLER_KEEP_RECENT_COUNT', '15000')),  # 基于实际去重效果调整
        }
    
    def update_data_queues(self, spider_name: str):
        """更新数据队列（仅限 master 节点）"""
        if not self.can_update_data:
            logger.warning(f"⚠️  worker 节点无权更新数据队列: {spider_name}")
            return False

        try:
            logger.info(f"📋 开始更新数据队列: {spider_name}")

            # 确保队列可用（这会触发队列填充）
            queue_available = self.queue_manager.ensure_queue_available(spider_name, force_refresh=False)
            if queue_available:
                logger.info(f"✅ 数据队列更新成功: {spider_name}")
                return True
            else:
                logger.error(f"❌ 数据队列更新失败: {spider_name}")
                return False

        except Exception as e:
            logger.error(f"❌ 更新数据队列异常: {spider_name}, 错误: {e}")
            return False

    def cleanup_redis_data(self, spider_name: str = None):
        """清理 Redis 数据，防止存储过多"""
        if not self.can_update_data:
            logger.warning(f"⚠️  worker 节点无权清理 Redis 数据")
            return False

        try:
            from crawl.tools.stocks import get_redis_connection
            redis_conn = get_redis_connection()
            if not redis_conn:
                logger.error("❌ Redis 连接不可用")
                return False

            # 获取所有队列信息
            queues_to_check = {
                'xueqiu-detail': 'xueqiu-detail:start_urls',
                'eastmoney-min': 'eastmoney-min:start_urls',
                'stock-detail': 'stock-detail:start_urls'
            }

            if spider_name and spider_name in queues_to_check:
                # 清理指定队列
                queues_to_check = {spider_name: queues_to_check[spider_name]}

            total_cleaned = 0
            for queue_name, queue_key in queues_to_check.items():
                # 统一使用SET类型操作
                current_size = redis_conn.scard(queue_key)
                max_size = self.redis_management['max_queue_size']

                logger.info(f"📊 检查队列 {queue_name}: 当前大小 {current_size:,}")

                if current_size > self.redis_management['cleanup_threshold']:
                    # 需要清理
                    keep_count = self.redis_management['keep_recent_count']
                    to_remove = current_size - keep_count

                    logger.warning(f"⚠️  队列 {queue_name} 过大 ({current_size:,})，开始清理...")

                    # 随机移除一些元素（保持队列多样性）
                    removed_count = 0
                    while removed_count < to_remove and redis_conn.scard(queue_key) > keep_count:
                        # 随机弹出元素
                        element = redis_conn.spop(queue_key)
                        if element:
                            removed_count += 1
                        else:
                            break

                    total_cleaned += removed_count
                    new_size = redis_conn.scard(queue_key)
                    logger.info(f"✅ 队列 {queue_name} 清理完成: {current_size:,} → {new_size:,} (清理 {removed_count:,} 个)")

                elif current_size > max_size:
                    logger.warning(f"⚠️  队列 {queue_name} 接近阈值 ({current_size:,}/{max_size:,})")
                else:
                    logger.info(f"✅ 队列 {queue_name} 大小正常 ({current_size:,}/{max_size:,})")

            if total_cleaned > 0:
                logger.info(f"🧹 Redis 数据清理完成，共清理 {total_cleaned:,} 个元素")
            else:
                logger.info("✅ Redis 数据大小正常，无需清理")

            return True

        except Exception as e:
            logger.error(f"❌ Redis 数据清理失败: {e}")
            return False

    def check_redis_memory_usage(self):
        """检查 Redis 内存使用情况"""
        try:
            from crawl.tools.stocks import get_redis_connection
            redis_conn = get_redis_connection()
            if not redis_conn:
                return None

            # 获取 Redis 内存信息
            info = redis_conn.info('memory')
            used_memory = info.get('used_memory', 0)
            used_memory_human = info.get('used_memory_human', 'N/A')
            used_memory_peak = info.get('used_memory_peak', 0)
            used_memory_peak_human = info.get('used_memory_peak_human', 'N/A')

            logger.info(f"📊 Redis 内存使用情况:")
            logger.info(f"  当前使用: {used_memory_human}")
            logger.info(f"  峰值使用: {used_memory_peak_human}")

            # 检查是否需要清理
            if used_memory > used_memory_peak * 0.8:
                logger.warning("⚠️  Redis 内存使用接近峰值，建议清理数据")
                return True  # 需要清理

            return False  # 不需要清理

        except Exception as e:
            logger.error(f"❌ 检查 Redis 内存使用失败: {e}")
            return None

    # 注释：自动执行爬虫的代码已被注释，现在只做数据更新
    # def start_spider(self, spider_name: str, count: int = 1):
    #     """启动爬虫（已注释 - 仅做数据更新）"""
    #     try:
    #         logger.info(f"准备启动爬虫: {spider_name}, 实例数: {count}")
    #
    #         # 确保队列可用
    #         queue_available = self.queue_manager.ensure_queue_available(spider_name)
    #
    #         if not queue_available:
    #             logger.error(f"队列不可用，取消启动爬虫 {spider_name}")
    #             return False
    #
    #         # 调用 scrapyd_manager 启动爬虫
    #         import subprocess
    #         cmd = [
    #             'python', 'scrapyd_manager.py', 'start',
    #             '--spider', spider_name,
    #             '--count', str(count),
    #             '--machine-id', self.machine_id
    #         ]
    #
    #         result = subprocess.run(cmd, capture_output=True, text=True)
    #
    #         if result.returncode == 0:
    #             logger.info(f"爬虫 {spider_name} 启动成功")
    #             return True
    #         else:
    #             logger.error(f"爬虫 {spider_name} 启动失败: {result.stderr}")
    #             return False
    #
    #     except Exception as e:
    #         logger.error(f"启动爬虫时发生错误: {e}")
    #         return False
    
    def schedule_frequent_crawling(self):
        """频繁数据更新调度 - 轻量级数据更新"""
        interval = SCHEDULE_CONFIG['frequent_minutes']
        logger.info(f"设置频繁数据更新调度: 每 {interval} 分钟执行一次")

        def job():
            logger.info(f"执行频繁数据更新任务 - 间隔 {interval} 分钟")
            # 频繁更新：主要是实时性要求高的数据
            self.update_data_queues("xueqiu-detail")  # 雪球实时数据

        schedule.every(interval).minutes.do(job)

        # 立即执行一次
        job()
    
    def schedule_periodic_crawling(self):
        """周期性数据更新调度 - 全量数据更新"""
        interval = SCHEDULE_CONFIG['periodic_minutes']
        logger.info(f"设置周期性数据更新调度: 每 {interval} 分钟执行一次")

        def job():
            logger.info(f"执行周期性数据更新任务 - 间隔 {interval} 分钟")
            # 全量更新：所有类型的数据
            self.update_data_queues("xueqiu-detail")    # 雪球详情数据
            self.update_data_queues("eastmoney-min")    # 东财分时数据
            self.update_data_queues("stock-detail")     # 股票详情数据（多数据源）

        schedule.every(interval).minutes.do(job)

        # 立即执行一次
        job()
    
    def schedule_master_maintenance(self):
        """主节点维护调度 - 队列监控和数据完整性检查"""
        interval = SCHEDULE_CONFIG['maintenance_minutes']
        logger.info(f"设置主节点维护调度: 每 {interval} 分钟检查队列状态")

        def maintenance_job():
            logger.info(f"执行主节点维护任务 - 间隔 {interval} 分钟")
            try:
                # 检查所有队列状态（不强制刷新，仅检查）
                self.queue_manager.ensure_queue_available("xueqiu-detail", force_refresh=False)
                self.queue_manager.ensure_queue_available("eastmoney-min", force_refresh=False)
                self.queue_manager.ensure_queue_available("stock-detail", force_refresh=False)

                # 显示详细状态
                info = self.queue_manager.get_queue_info()
                queue_status = info.get('queue_status', {})
                logger.info(f"📊 队列状态检查完成:")
                logger.info(f"  雪球详情: {queue_status.get('xueqiu_detail_queue_length', 0):,}")
                logger.info(f"  东财分时: {queue_status.get('eastmoney_min_queue_length', 0):,}")
                logger.info(f"  股票详情: {queue_status.get('stock_detail_queue_length', 0):,}")
                logger.info(f"  失败数量: {queue_status.get('failed_urls_count', 0):,}")

                # 检查 Redis 内存使用情况
                need_cleanup = self.check_redis_memory_usage()
                if need_cleanup:
                    logger.info("🧹 开始 Redis 数据清理...")
                    self.cleanup_redis_data()

            except Exception as e:
                logger.error(f"❌ 主节点维护任务失败: {e}")

        schedule.every(interval).minutes.do(maintenance_job)

    def schedule_data_refresh(self):
        """数据刷新调度 - 定期强制刷新队列"""
        interval = SCHEDULE_CONFIG['refresh_minutes']
        logger.info(f"设置数据刷新调度: 每 {interval} 分钟强制刷新队列")

        def refresh_job():
            logger.info(f"执行数据刷新任务 - 强制更新所有队列，间隔 {interval} 分钟")
            if not self.can_update_data:
                logger.warning("⚠️  worker 节点无权执行数据刷新")
                return

            try:
                # 强制刷新所有队列
                self.queue_manager.ensure_queue_available("xueqiu-detail", force_refresh=True)
                self.queue_manager.ensure_queue_available("eastmoney-min", force_refresh=True)
                self.queue_manager.ensure_queue_available("stock-detail", force_refresh=True)

                logger.info("✅ 所有队列强制刷新完成")

            except Exception as e:
                logger.error(f"❌ 数据刷新任务失败: {e}")

        schedule.every(interval).minutes.do(refresh_job)

    def schedule_data_cleanup(self):
        """数据清理调度 - 定期清理 Redis 数据，防止存储过多"""
        cleanup_interval = int(os.getenv('CRAWLER_CLEANUP_MINUTES', '480'))  # 默认8小时
        logger.info(f"设置数据清理调度: 每 {cleanup_interval} 分钟清理 Redis 数据")

        def cleanup_job():
            logger.info(f"执行数据清理任务 - 间隔 {cleanup_interval} 分钟")
            if not self.can_update_data:
                logger.warning("⚠️  worker 节点无权执行数据清理")
                return

            try:
                # 检查内存使用情况
                need_cleanup = self.check_redis_memory_usage()

                # 强制清理所有队列
                logger.info("🧹 开始定期数据清理...")
                self.cleanup_redis_data()

                logger.info("✅ 定期数据清理完成")

            except Exception as e:
                logger.error(f"❌ 数据清理任务失败: {e}")

        schedule.every(cleanup_interval).minutes.do(cleanup_job)

    def schedule_smart_crawling(self):
        """智能数据更新调度 - 根据数据类型设置不同更新频率"""
        realtime_interval = SCHEDULE_CONFIG['smart_realtime_minutes']
        minute_interval = SCHEDULE_CONFIG['smart_minute_minutes']
        detail_interval = SCHEDULE_CONFIG['smart_detail_minutes']

        logger.info("设置智能数据更新调度")
        logger.info(f"  实时数据: 每 {realtime_interval} 分钟")
        logger.info(f"  分时数据: 每 {minute_interval} 分钟")
        logger.info(f"  详情数据: 每 {detail_interval} 分钟")

        # 实时数据更新
        def realtime_job():
            logger.info(f"执行实时数据更新 - 间隔 {realtime_interval} 分钟")
            self.update_data_queues("xueqiu-detail")

        # 分时数据更新
        def minute_job():
            logger.info(f"执行分时数据更新 - 间隔 {minute_interval} 分钟")
            self.update_data_queues("eastmoney-min")

        # 详情数据更新
        def detail_job():
            logger.info(f"执行详情数据更新 - 间隔 {detail_interval} 分钟")
            self.update_data_queues("stock-detail")

        # 设置不同的更新频率
        schedule.every(realtime_interval).minutes.do(realtime_job)   # 实时数据
        schedule.every(minute_interval).minutes.do(minute_job)       # 分时数据
        schedule.every(detail_interval).minutes.do(detail_job)       # 详情数据

        # 立即执行一次
        realtime_job()
        minute_job()
        detail_job()

    def schedule_config_based_crawling(self):
        """基于配置的智能数据更新调度"""
        logger.info("设置基于配置的智能数据更新调度")

        # 获取所有启用的爬虫配置
        enabled_spiders = data_update_config.get_all_enabled_spiders()

        for spider_config in enabled_spiders:
            schedule_config = data_update_config.get_schedule_config(spider_config.frequency)
            interval_minutes = schedule_config.get("interval_minutes", 60)

            def create_job(spider_name):
                def job():
                    logger.info(f"执行配置数据更新: {spider_name} (间隔: {interval_minutes}分钟)")
                    self.update_data_queues(spider_name)
                return job

            job_func = create_job(spider_config.name)

            # 根据频率设置调度
            if spider_config.frequency == UpdateFrequency.REALTIME:
                schedule.every(interval_minutes).minutes.do(job_func)
            elif spider_config.frequency == UpdateFrequency.FREQUENT:
                schedule.every(interval_minutes).minutes.do(job_func)
            elif spider_config.frequency == UpdateFrequency.REGULAR:
                schedule.every(interval_minutes).minutes.do(job_func)
            elif spider_config.frequency == UpdateFrequency.PERIODIC:
                schedule.every(interval_minutes).minutes.do(job_func)
            elif spider_config.frequency == UpdateFrequency.DAILY:
                schedule.every(interval_minutes).minutes.do(job_func)

            logger.info(f"已设置 {spider_config.name} 数据更新调度: 每 {interval_minutes} 分钟执行")

            # 立即执行一次（仅高优先级）
            if spider_config.priority.value == "high":
                job_func()

    def run_scheduler(self):
        """运行调度器"""
        logger.info("启动分布式爬虫调度器")
        
        try:
            while True:
                schedule.run_pending()
                time.sleep(30)  # 每30秒检查一次
                
        except KeyboardInterrupt:
            logger.info("收到停止信号，正在关闭调度器...")
        except Exception as e:
            logger.error(f"调度器运行错误: {e}")


def main():
    parser = argparse.ArgumentParser(description='分布式爬虫调度器')
    parser.add_argument('--machine-id', required=True, help='机器ID')
    parser.add_argument('--role', choices=['master', 'worker'], default='worker',
                       help='机器角色: master(主节点) 或 worker(工作节点)')
    parser.add_argument('--mode', choices=['master', 'worker', 'smart', 'config'],
                       help='调度模式: master(主节点), worker(工作节点), smart(智能调度), config(配置调度)')
    parser.add_argument('--spider', choices=['xueqiu-detail', 'eastmoney-min'],
                       help='单次执行的爬虫类型')
    parser.add_argument('--count', type=int, default=1, help='爬虫实例数量')
    parser.add_argument('--once', action='store_true', help='只执行一次，不启动调度器')
    
    args = parser.parse_args()
    
    # 创建分布式爬虫实例
    crawler = DistributedCrawler(args.machine_id, args.role)
    
    if args.once:
        # 单次执行模式
        if args.spider:
            success = crawler.start_spider(args.spider, args.count)
            sys.exit(0 if success else 1)
        else:
            print("单次执行模式需要指定 --spider 参数")
            sys.exit(1)
    
    # 调度模式
    if args.mode == 'master':
        print("🎯 主节点模式: 配置数据更新调度 + 维护 + 数据刷新 + 数据清理")
        crawler.schedule_config_based_crawling()
        crawler.schedule_master_maintenance()
        crawler.schedule_data_refresh()
        crawler.schedule_data_cleanup()
    elif args.mode == 'worker':
        print("⚠️  工作节点模式: 仅允许监控，不执行数据更新")
        print("💡 提示: 只有 master 节点可以执行数据更新操作")
        # worker 节点只执行维护检查，不更新数据
        crawler.schedule_master_maintenance()
    elif args.mode == 'smart':
        print("🧠 智能调度模式: 智能数据更新调度")
        crawler.schedule_smart_crawling()
    elif args.mode == 'config':
        print("⚙️  配置调度模式: 基于配置的数据更新调度")
        crawler.schedule_config_based_crawling()
    else:
        # 默认根据角色自动选择模式
        if args.role == 'master':
            print("🎯 默认主节点模式: 配置数据更新调度 + 维护 + 数据刷新 + 数据清理")
            crawler.schedule_config_based_crawling()
            crawler.schedule_master_maintenance()
            crawler.schedule_data_refresh()
            crawler.schedule_data_cleanup()
        else:
            print("⚠️  默认工作节点模式: 仅允许监控，不执行数据更新")
            print("💡 提示: 只有 master 节点可以执行数据更新操作")
            # worker 节点只执行维护检查，不更新数据
            crawler.schedule_master_maintenance()
    
    # 运行调度器
    crawler.run_scheduler()


if __name__ == '__main__':
    main()
