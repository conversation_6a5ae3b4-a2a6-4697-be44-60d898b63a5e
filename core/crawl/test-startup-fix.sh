#!/bin/bash

# 测试 Docker 启动修复脚本

echo "🧪 测试 Docker 启动顺序修复"
echo "================================"

# 检查修复的关键点
echo "🔍 检查启动顺序修复..."

# 检查 worker 模式
echo "📋 检查 worker 模式启动顺序:"
grep -A 20 '"worker")' docker-entrypoint.sh | grep -E "(scrapyd|deploy_project)" | nl

echo ""

# 检查 smart 模式  
echo "📋 检查 smart 模式启动顺序:"
grep -A 20 '"smart")' docker-entrypoint.sh | grep -E "(scrapyd|deploy_project)" | nl

echo ""

# 检查 config 模式
echo "📋 检查 config 模式启动顺序:"
grep -A 20 '"config")' docker-entrypoint.sh | grep -E "(scrapyd|deploy_project)" | nl

echo ""
echo "✅ 修复验证完成"
echo ""
echo "🚀 现在可以重新构建并测试 Docker 镜像:"
echo "   docker build -t blackbear-crawler:latest ."
echo "   docker run blackbear-crawler:latest worker"
