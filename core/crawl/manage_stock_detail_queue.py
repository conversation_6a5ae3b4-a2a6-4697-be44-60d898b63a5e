#!/usr/bin/env python3
"""
股票详情数据队列管理工具
支持多数据源：雪球收入、雪球热门文章、东方财富营收占比、东方财富股东持仓
"""

import sys
import argparse
import json
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))

from crawl.tools.stocks import (
    get_queue_status, 
    populate_stock_detail_queue,
    get_redis_connection,
    stock_detail_queue
)


def show_status():
    """显示队列状态"""
    print("📊 股票详情数据队列状态")
    print("=" * 50)
    
    status = get_queue_status()
    if not status:
        print("❌ 无法获取队列状态")
        return
    
    print(f"📊 股票详情队列长度: {status['stock_detail_queue_length']}")
    print(f"📊 雪球详情队列长度: {status['xueqiu_detail_queue_length']}")
    print(f"📊 东财分时队列长度: {status['eastmoney_min_queue_length']}")
    print(f"❌ 失败URL数量: {status['failed_urls_count']}")
    print(f"⚙️  最小队列大小: {status['min_queue_size']}")
    print(f"⚙️  批处理大小: {status['batch_size']}")
    print(f"⚙️  最大失败次数: {status['max_failed_count']}")
    
    # 计算股票数量（每个股票4个URL）
    stock_count = status['stock_detail_queue_length'] // 4
    print(f"📈 预计股票数量: {stock_count} 个（每股票4种数据源）")


def populate_queue(force_full=True):
    """填充队列
    
    Args:
        force_full (bool): 是否强制全量填充，默认True
    """
    mode = "全量" if force_full else "增量"
    print(f"📋 开始{mode}填充股票详情数据队列...")
    
    success = populate_stock_detail_queue(force_full=force_full)
    if success:
        print(f"✅ 队列{mode}填充成功")
        show_status()
    else:
        print(f"❌ 队列{mode}填充失败")


def clear_queue():
    """清空队列"""
    print("🗑️  清空股票详情数据队列...")
    
    redis_conn = get_redis_connection()
    if not redis_conn:
        print("❌ Redis连接失败")
        return
    
    try:
        count = redis_conn.scard(stock_detail_queue)
        redis_conn.delete(stock_detail_queue)
        print(f"✅ 已清空 {count} 个URL")
    except Exception as e:
        print(f"❌ 清空队列失败: {e}")


def cleanup_failed():
    """清理失败记录"""
    print("🧹 清理失败记录...")
    
    redis_conn = get_redis_connection()
    if not redis_conn:
        print("❌ Redis连接失败")
        return
    
    try:
        failed_keys = redis_conn.keys('failed:*')
        if failed_keys:
            redis_conn.delete(*failed_keys)
            print(f"✅ 已清理 {len(failed_keys)} 个失败记录")
        else:
            print("✅ 没有失败记录需要清理")
    except Exception as e:
        print(f"❌ 清理失败记录失败: {e}")


def sample_urls(count=5):
    """查看队列中的URL样例"""
    print(f"🔍 查看队列中的 {count} 个URL样例...")
    
    redis_conn = get_redis_connection()
    if not redis_conn:
        print("❌ Redis连接失败")
        return
    
    try:
        urls = list(redis_conn.sscan(stock_detail_queue, count=count)[1])
        if urls:
            print("\n样例URL数据:")
            for i, url in enumerate(urls[:count], 1):
                url_str = url.decode('utf-8') if isinstance(url, bytes) else url
                try:
                    url_data = json.loads(url_str)
                    print(f"  {i}. 股票: {url_data.get('code', 'N/A')}, "
                          f"类型: {url_data.get('data_type', 'N/A')}")
                    print(f"     URL: {url_data.get('url', 'N/A')[:80]}...")
                except json.JSONDecodeError:
                    print(f"  {i}. 原始数据: {url_str[:100]}...")
                print()
        else:
            print("队列为空")
    except Exception as e:
        print(f"❌ 获取URL样例失败: {e}")


def analyze_queue():
    """分析队列数据分布"""
    print("📈 分析队列数据分布...")
    
    redis_conn = get_redis_connection()
    if not redis_conn:
        print("❌ Redis连接失败")
        return
    
    try:
        # 获取所有URL数据
        all_urls = redis_conn.smembers(stock_detail_queue)
        
        if not all_urls:
            print("队列为空")
            return
        
        # 统计数据类型分布
        data_types = {}
        stock_codes = set()
        
        for url_data in all_urls:
            try:
                url_str = url_data.decode('utf-8') if isinstance(url_data, bytes) else url_data
                data = json.loads(url_str)
                
                data_type = data.get('data_type', 'unknown')
                stock_code = data.get('code', 'unknown')
                
                data_types[data_type] = data_types.get(data_type, 0) + 1
                stock_codes.add(stock_code)
                
            except json.JSONDecodeError:
                data_types['invalid'] = data_types.get('invalid', 0) + 1
        
        print(f"\n📊 队列数据分析:")
        print(f"  总URL数量: {len(all_urls)}")
        print(f"  股票数量: {len(stock_codes)}")
        print(f"  数据类型分布:")
        for data_type, count in data_types.items():
            print(f"    {data_type}: {count}")
        
        # 检查数据完整性
        expected_total = len(stock_codes) * 3  # 每个股票3种数据源
        actual_total = sum(data_types.values())
        
        if actual_total == expected_total:
            print(f"  ✅ 数据完整性: 正常 ({actual_total}/{expected_total})")
        else:
            print(f"  ⚠️  数据完整性: 异常 ({actual_total}/{expected_total})")
            
    except Exception as e:
        print(f"❌ 分析队列失败: {e}")


def main():
    parser = argparse.ArgumentParser(description='股票详情数据队列管理工具')
    parser.add_argument('command', choices=['status', 'populate', 'clear', 'cleanup', 'sample', 'analyze'],
                       help='执行的命令')
    parser.add_argument('--count', type=int, default=5,
                       help='sample命令显示的URL数量')
    parser.add_argument('--incremental', action='store_true',
                       help='使用增量填充模式（默认为全量填充）')
    
    args = parser.parse_args()
    
    # 确定填充模式
    force_full = not args.incremental
    
    print("📈 股票详情数据队列管理工具")
    print("支持多数据源：雪球收入、雪球热门文章、东方财富营收占比、东方财富股东持仓")
    print("=" * 60)
    
    if args.command == 'status':
        show_status()
    elif args.command == 'populate':
        populate_queue(force_full=force_full)
    elif args.command == 'clear':
        clear_queue()
    elif args.command == 'cleanup':
        cleanup_failed()
    elif args.command == 'sample':
        sample_urls(args.count)
    elif args.command == 'analyze':
        analyze_queue()


if __name__ == '__main__':
    main()
