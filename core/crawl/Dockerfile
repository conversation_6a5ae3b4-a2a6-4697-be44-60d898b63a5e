# Blackbear 分布式爬虫优化版 Dockerfile
# 多阶段构建，减小镜像体积

# ==================== 构建阶段 ====================
FROM python:3.10.9-slim AS builder

# 设置构建环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 安装构建依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        build-essential \
        gcc \
        g++ \
        curl \
        wget \
        && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /build

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖到临时目录
RUN pip install --no-cache-dir --user -r requirements.txt && \
    python -c "import redis; print('Redis installed successfully, version:', redis.__version__)"

# ==================== 运行阶段 ====================
FROM python:3.10.9-slim AS runtime

# 镜像元数据
LABEL maintainer="Blackbear Team"
LABEL description="Blackbear Distributed Crawler - Optimized"
LABEL version="1.0"

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV TZ=Asia/Shanghai
ENV DEBIAN_FRONTEND=noninteractive

# 安装运行时依赖
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        curl \
        wget \
        ca-certificates \
        && rm -rf /var/lib/apt/lists/* \
        && apt-get clean

# 设置时区
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# 创建非 root 用户
RUN groupadd -r crawler && \
    useradd -r -g crawler -d /code -s /bin/bash crawler

# 设置工作目录
WORKDIR /code

# 从构建阶段复制 Python 包
COPY --from=builder --chown=crawler:crawler /root/.local /home/<USER>/.local

# 验证关键包是否正确安装
RUN python -c "import redis; print('✅ Redis module available, version:', redis.__version__)" || \
    (echo "❌ Redis module not found, installing..." && \
     pip install --user redis==5.0.1 scrapy-redis==0.9.1)

# 复制项目文件
COPY --chown=crawler:crawler . /code/

# 复制 Scrapyd 配置
COPY --chown=crawler:crawler ./scrapyd.conf /etc/scrapyd/

# 设置启动脚本权限
# 注意：docker-start-scrapyd.sh 已修复启动顺序问题
# 修复内容：先启动 Scrapyd 服务，等待服务就绪后再部署项目
RUN chmod +x /code/docker-entrypoint.sh /code/docker-start-scrapyd.sh

# 创建必要的目录并设置权限
RUN mkdir -p /code/logs /code/data /var/log/scrapyd && \
    chown -R crawler:crawler /code /var/log/scrapyd && \
    chmod 755 /code/logs /code/data

# 设置 Python 路径
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/code:/home/<USER>/.local/lib/python3.10/site-packages

# 分布式爬虫环境变量由 Docker Compose 动态设置
# 不在镜像中硬编码默认值，保持灵活性

# Scrapyd 配置
ENV SCRAPYD_BIND_ADDRESS=0.0.0.0
ENV SCRAPYD_HTTP_PORT=6800

# 切换到非 root 用户
USER crawler

# 暴露端口
EXPOSE 6800

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:6800/daemonstatus.json || exit 1

# 设置启动脚本
# 支持多种启动模式，由外部传入参数决定:
# docker run image worker                      -> 使用工作节点模式
# docker run image master                      -> 使用主节点模式
# docker run image smart                       -> 使用智能调度模式
# docker run image config                      -> 使用配置调度模式
ENTRYPOINT ["/code/docker-entrypoint.sh"]
