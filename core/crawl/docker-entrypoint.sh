#!/bin/bash

# 分布式爬虫 Docker 容器启动脚本 (精简版)
# 支持模式: master, worker, smart, config

set -e

echo "🐳 启动分布式爬虫容器"
echo "模式: ${1:-${CRAWLER_MODE:-master}}"
echo "角色: ${CRAWLER_ROLE:-master}"
echo "机器ID: ${CRAWLER_MACHINE_ID:-docker-001}"
echo "调度: ${CRAWLER_SCHEDULE:-未设置}"

# 等待Redis连接
echo "⏳ 等待Redis连接..."
python3 -c "
import sys
sys.path.append('/code')
from crawl.tools.stocks import get_redis_connection
import time

max_retries = 30
for i in range(max_retries):
    try:
        redis_conn = get_redis_connection()
        if redis_conn and redis_conn.ping():
            print('✅ Redis连接成功')
            break
    except Exception as e:
        if i == max_retries - 1:
            print(f'❌ Redis连接失败: {e}')
            sys.exit(1)
        time.sleep(2)
"

# 根据模式启动不同服务
case "${1:-${CRAWLER_MODE:-master}}" in
    "worker")
        echo "👷 启动工作节点模式 (Worker节点)"
        # 部署项目
        python3 deploy_project.py

        # 启动scrapyd（后台）
        scrapyd &
        sleep 10

        # 启动工作节点
        python3 distributed_crawler.py \
            --machine-id "$CRAWLER_MACHINE_ID" \
            --role "worker" \
            --mode "worker"
        ;;
    "master")
        echo "👑 启动主节点模式 (Master节点)"
        # 部署项目
        echo "📦 部署爬虫项目..."
        if ! python3 deploy_project.py; then
            echo "❌ 项目部署失败，但继续启动服务"
        fi

        # 启动scrapyd（后台）
        echo "🚀 启动Scrapyd服务..."
        scrapyd &
        sleep 15

        # 检查scrapyd是否启动成功
        for i in {1..10}; do
            if curl -s http://localhost:6800/daemonstatus.json > /dev/null 2>&1; then
                echo "✅ Scrapyd服务启动成功"
                break
            fi
            echo "⏳ 等待Scrapyd启动... ($i/10)"
            sleep 3
        done

        # 初始化数据队列（立即执行一次）
        echo "🔄 初始化数据队列..."
        if python3 -c "
import sys
sys.path.append('/code')
from distributed_crawler import DistributedCrawler

# 创建master节点实例
crawler = DistributedCrawler('$CRAWLER_MACHINE_ID', 'master')

# 立即更新所有数据队列
print('📋 更新雪球详情数据队列...')
crawler.update_data_queues('xueqiu-detail')

print('📋 更新东财分时数据队列...')
crawler.update_data_queues('eastmoney-min')

print('📋 更新股票详情数据队列...')
crawler.update_data_queues('stock-detail')

print('✅ 数据队列初始化完成')
"; then
            echo "✅ 数据队列初始化成功"
        else
            echo "❌ 数据队列初始化失败，但继续启动服务"
        fi

        # 启动主节点
        echo "🚀 启动主节点调度器..."
        python3 distributed_crawler.py \
            --machine-id "$CRAWLER_MACHINE_ID" \
            --role "master" \
            --mode "master"
        ;;
    "smart")
        echo "🧠 启动智能调度模式 (Master节点)"
        # 部署项目
        python3 deploy_project.py

        # 启动scrapyd（后台）
        scrapyd &
        sleep 10

        # 初始化数据队列（立即执行一次）
        echo "🔄 初始化数据队列..."
        python3 -c "
import sys
sys.path.append('/code')
from distributed_crawler import DistributedCrawler

# 创建master节点实例
crawler = DistributedCrawler('$CRAWLER_MACHINE_ID', 'master')

# 立即更新所有数据队列
print('📋 更新雪球详情数据队列...')
crawler.update_data_queues('xueqiu-detail')

print('📋 更新东财分时数据队列...')
crawler.update_data_queues('eastmoney-min')

print('📋 更新股票详情数据队列...')
crawler.update_data_queues('stock-detail')

print('✅ 数据队列初始化完成')
"

        # 启动智能调度器
        echo "🚀 启动智能调度器..."
        python3 distributed_crawler.py \
            --machine-id "$CRAWLER_MACHINE_ID" \
            --role "master" \
            --mode "smart"
        ;;
    "config")
        echo "⚙️  启动配置调度模式 (Master节点) - Docker优化版"
        # 部署项目
        python3 deploy_project.py

        # 启动scrapyd（后台）
        scrapyd &
        sleep 10

        # 初始化数据队列（立即执行一次）
        echo "🔄 初始化数据队列..."
        python3 -c "
import sys
sys.path.append('/code')
from distributed_crawler import DistributedCrawler

# 创建master节点实例
crawler = DistributedCrawler('$CRAWLER_MACHINE_ID', 'master')

# 立即更新所有数据队列
print('📋 更新雪球详情数据队列...')
crawler.update_data_queues('xueqiu-detail')

print('📋 更新东财分时数据队列...')
crawler.update_data_queues('eastmoney-min')

print('📋 更新股票详情数据队列...')
crawler.update_data_queues('stock-detail')

print('✅ 数据队列初始化完成')
"

        # 启动配置调度器（包含维护、刷新、清理）
        echo "🚀 启动配置调度器..."
        python3 distributed_crawler.py \
            --machine-id "$CRAWLER_MACHINE_ID" \
            --role "master" \
            --mode "config"
        ;;
    *)
        echo "❌ 未知模式: ${1:-${CRAWLER_MODE:-master}}"
        echo "支持的模式: worker, master, smart, config"
        echo ""
        echo "使用方法:"
        echo "  docker run image worker             # 工作节点模式 (仅执行任务)"
        echo "  docker run image master             # 主节点模式 (自动更新数据+调度)"
        echo "  docker run image smart              # 智能调度模式 (自动更新数据+智能调度)"
        echo "  docker run image config             # 配置调度模式 (自动更新数据+配置调度，推荐)"
        echo ""
        echo "📋 数据更新说明:"
        echo "  ✅ master/smart/config 模式: 自动初始化并更新数据队列"
        echo "  ❌ worker 模式: 仅执行任务，不更新数据队列"
        exit 1
        ;;
esac
