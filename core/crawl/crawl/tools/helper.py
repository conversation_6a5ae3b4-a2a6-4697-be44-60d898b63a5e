# -*- coding: utf-8 -*-
import re
import emoji
import urllib.parse
import requests
import random
from scrapy.utils.project import get_project_settings
import datetime
import time
import http.client
import json
from crawl.db.db import DBSession, Task, Quant
import pinyin
import re
from http.cookies import SimpleCookie

def getFirstSentence(text):
    # 使用正则表达式匹配第一个句子（直到句号、感叹号、问号或换行符）
    match = re.search(r'^.*?[。！？!?\n]', text, re.DOTALL)
    
    if not match:
        return text.strip()[:100]  # 无匹配时返回去除首尾空格的原文
    
    # 提取匹配结果并去除末尾换行符及首尾空格
    title = match.group()
    # 替换末尾的换行符（如果有）
    if title.endswith('\n'):
        title = title[:-1]
    
    title = title.strip()
    
    if len(title) > 100:
        title = title[:100] + '...'
    
    return title


def send_message(msg:str):
    url = "https://api.anpush.com/push/BWZ6UZ94QJW7JJSU6WVAMM1N7FRP02"

    payload = {
        "title": msg,
        "content": msg,
        "channel": "61409"
    }

    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }

    response = requests.post(url, headers=headers, data=payload)

def get_xueqiu_cookie():
    """
    获取雪球cookie
    """
    session = DBSession()
    cookie = session.query(Quant).filter(Quant.name=='cookie').all()[0]
    cookie_arr = SimpleCookie(cookie.xueqiuCookie)
    cookies = {key: morsel.value for key, morsel in cookie_arr.items()}
    return cookies

def get_weibo_cookie():
    """
    获取微博cookie
    """
    session = DBSession()
    cookie = session.query(Quant).filter(Quant.name=='cookie').all()[0]
    cookie_arr = SimpleCookie(cookie.weiboCookie)
    cookies = {key: morsel.value for key, morsel in cookie_arr.items()}
    return cookies

def filter_emoji(desstr, restr=''):
    # 过滤表情
    try:
        co = re.compile(u'[\U00010000-\U0010ffff]')
    except re.error:
        co = re.compile(u'[\uD800-\uDBFF][\uDC00-\uDFFF]')
    return co.sub(restr, desstr)

def change_into(val):
    if val == '-':
        return None
    return val

def get_pinyin_first_alpha(name):
    return "".join([i[0] for i in pinyin.get(name, " ").split(" ")])

def fetch_quant_raw():
    """
     获取量化配置
    """
    session = DBSession()
    quant = session.query(Quant).filter(Quant.name=='quant').all()[0]

    return quant

def ago_minutes_timestr(minutes, strf='%Y-%m-%d %H:%M:%S'):
    dayAgo = datetime.datetime.now() - datetime.timedelta(minutes=minutes)
    return dayAgo.strftime(strf)

def ago_day_timestr(days, strf='%Y-%m-%d %H:%M:%S'):
    dayAgo = datetime.datetime.now() - datetime.timedelta(days=days)
    return dayAgo.strftime(strf)

def after_day_timestr(days, strf='%Y-%m-%d %H:%M:%S'):
    dayAgo = datetime.datetime.now() + datetime.timedelta(days=days)
    return dayAgo.strftime(strf)

def ago_day_timestamp(days, ms=True):
    dayAgo = datetime.datetime.now() - datetime.timedelta(days=days)

    if ms:
        return int(time.mktime(dayAgo.timetuple()) * 1000.0 + dayAgo.microsecond / 1000.0)

def today_timestr(strf='%Y-%m-%d'):
    datetime.date.today()

    today = datetime.date.today()
    today_time = int(time.mktime(today.timetuple()))
    return time.strftime(strf, time.localtime(today_time))

def today_timestamp(ms=True):
    datetime.date.today()

    today = datetime.date.today()
    today_time = int(time.mktime(today.timetuple()))

    if ms:
        return today_time * 1000
    else:
        return today_time

    

def random_user_agent():
    settings = get_project_settings()
    return random.choice(settings['USER_AGENT'])

def get_first_cookies(url):
    headers = {'User-Agent': random_user_agent()}
    r = requests.get(url, headers=headers)
    return r.cookies.get_dict()

# def send_message(msg: str):
#     qs = urllib.parse.urlencode(dict(
#         token='2a657ba0c649b678c63a34c96c82b729dd745a18',
#         msg=msg,
#     ))
#     rs = requests.get(url="https://sre24.com/api/v1/push?" + qs).json()
#     assert int(rs["code"] / 100) == 2, rs

# def notify_user(msg:str, prefix:str='', token:str="2a657ba0c649b678c63a34c96c82b729dd745a18", server:str="push.jwks123.com", port:int=443):
#     if not token or not msg:
#         return

#     if port == 443:
#         conn = http.client.HTTPSConnection(host=server, port=port)
#     else:
#         conn = http.client.HTTPConnection(host=server, port=port)
#     rqbody = json.dumps(dict(token=token, msg=prefix+msg))
#     conn.request(method="POST", url="/to/", body=rqbody)
#     resp = conn.getresponse()
#     rs = json.loads(resp.read().decode("utf8"))
#     assert int(rs["code"] / 100) == 2, rs

def is_not_number(num):
    pattern = re.compile(r'^[-+]?[-0-9]\d*\.\d*|[-+]?\.?[0-9]\d*$')
    result = pattern.match(num)

    if result:
        return False
    else:
        return True

def clear_html(src_html=''):
    content = re.sub(r"</?(.+?)>", "", str(src_html)) # 去除标签
    # content = re.sub(r"&nbsp;", "", content)
    dst_html = re.sub(r"\s+", "", content)  # 去除空白字符
    dst_html.replace('&nbsp;', '')
    return emoji.demojize(dst_html)

def stock_code(code):
    symbol_type = 'SZ'

    if code.startswith('6'):
        symbol_type = 'SH'

    if code.startswith('8'):
        symbol_type = 'BJ'

    if code.startswith('4'):
        symbol_type = 'BJ'
        
    symbol_id = symbol_type + code

    return symbol_id

def stock_code2(code):
    symbol_type = '0.'

    if code.startswith('6'):
        symbol_type = '1.'

    if code.startswith('8'):
        symbol_type = '0.'

    if code.startswith('4'):
        symbol_type = '0.'
        
    symbol_id = symbol_type + code

    return symbol_id

def spider_monitor(name, success):
    session = DBSession()
    taskID = 0

    if name == 'eastmoney':
        taskID = 17
    elif name == 'eastmoney2':
        taskID = 17
    elif name == 'eastmone3':
        taskID = 17
    elif name == 'datayes':
        taskID = 2
    elif name == 'cninfo':
        taskID = 4
    elif name == 'cninfo-relation':
        taskID = 5
    elif name == 'cls':
        taskID = 1
    elif name == 'fbe':
        taskID = 32
    elif name == 'eastmoney-caifuhao':
        taskID = 6
    elif name == 'eastmoney-bx':
        taskID = 7
    elif name == 'eastmoney-rzrq':
        taskID = 8
    elif name == 'stock-rank':
        taskID = 14
    elif name == 'eastmoney-hsgtcg':
        taskID = 12
    elif name == 'eastmoney-report':
        taskID = 15
    # elif name == 'xueqiu-report':
    #     taskID = 22
    elif name == 'xueqiu-news':
        taskID = 20
    elif name == 'xueqiu-income':
        taskID = 19
    elif name == 'xueqiu-media':
        taskID = 3
    elif name == 'eastmoney-hot':
        taskID = 11
    elif name == 'eastmoney-org':
        taskID = 13
    elif name == 'eastmoney-holder':
        taskID = 10
    elif name == 'eastmoney-debt':
        taskID = 9
    elif name == 'eastmoney-ylyc':
        taskID = 21
    elif name == 'eastmoney-research':
        taskID = 2
    elif name == 'eastmoney-business':
        taskID = 35
    elif name == 'shibor':
        taskID = 18
        
    task = session.query(Task).get(taskID)

    if task:
        if success:
            task.success = task.success + 1
        else: 
            task.fail = task.fail + 1
        task.lastUpdated = datetime.datetime.now()
        session.commit()
        session.close()
