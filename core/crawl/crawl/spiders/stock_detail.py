# -*- coding: utf-8 -*-

"""
股票详情爬虫
爬取个股的详细数据，包括收入、业务分析、热门文章等
数据源：xueqiu.com, eastmoney.com
"""

import json
import time
import hashlib

from crawl.items import MediaItem, Stock2Item, FundItem
from crawl.tools.spider_base import DataSpider, SpiderConfig
from crawl.tools.performance import timing_decorator
from crawl.tools.data_processor import create_stock_processor
from crawl.tools.helper import stock_code, ago_day_timestamp, ago_day_timestr, fetch_quant_raw, get_xueqiu_cookie, clear_html
from crawl.db.db import DBSession, Stock, Media
from scrapy.http import FormRequest
from sqlalchemy.dialects.mysql import insert
from scrapy_redis.spiders import RedisSpider


class StockDetailSpider(RedisSpider, DataSpider):
    """
    股票详情爬虫 - Redis分布式版本
    爬取个股的详细数据，包括收入、业务分析、热门文章等
    支持多种数据源的分布式爬取
    """
    name = 'stock-detail'
    allowed_domains = ['eastmoney.com', 'xueqiu.com']

    # Redis 分布式配置
    redis_key = 'stock-detail:start_urls'

    def __init__(self, code=None, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.code = code
        self.data_processor = create_stock_processor()

        try:
            self.quant = fetch_quant_raw()
        except Exception as e:
            self.logger.warning(f"Failed to fetch quant data: {e}")
            self.quant = None

        # Redis分布式模式下不需要单独的code参数
        if self.code:
            self.logger.info(f"Running in single stock mode for code: {self.code}")
        else:
            self.logger.info("Running in distributed mode, processing from Redis queue")

    def get_spider_config(self) -> SpiderConfig:
        """获取爬虫配置 - 个股详情使用保守配置"""
        return SpiderConfig(
            concurrent_requests=1,
            download_delay=2,
            batch_size=10,
            retry_times=2
        )

    def count_size(self, key):
        """重写count_size方法，统一使用SCARD，因为我们统一使用SET类型"""
        try:
            return self.server.scard(key)
        except Exception as e:
            self.logger.error(f"Error counting queue size for key {key}: {e}")
            return 0

    def get_start_urls(self) -> list:
        """获取起始URL列表"""
        return []  # Redis分布式模式不使用此方法

    def make_requests_from_data(self, data):
        """
        从Redis队列数据创建请求
        数据格式: JSON格式 {"url": "...", "code": "...", "data_type": "..."}
        """
        # 解码数据
        if isinstance(data, bytes):
            data = data.decode('utf-8')

        try:
            # 解析JSON数据
            url_data = json.loads(data.strip())

            url = url_data.get('url')
            code = url_data.get('code')
            data_type = url_data.get('data_type')

            if not all([url, code, data_type]):
                self.logger.error(f"Invalid URL data: {url_data}")
                return None

            # 调试：记录处理的URL
            self.logger.debug(f"[分布式爬虫] 处理 {data_type} 数据，股票: {code}")

            # 根据数据类型设置不同的请求参数
            if data_type in ['income', 'hot']:
                # 雪球API需要cookie
                cookies_xueqiu = get_xueqiu_cookie()
                return FormRequest(
                    url=url,
                    meta={'cookiejar': 1, 'code': code, 'data_type': data_type},
                    method="GET",
                    cookies=cookies_xueqiu,
                    callback=self.parse_response
                )
            elif data_type in ['business', 'holder']:
                # 东方财富API
                return FormRequest(
                    url=url,
                    meta={'cookiejar': 1, 'code': code, 'data_type': data_type},
                    method="GET",
                    callback=self.parse_response
                )
            else:
                self.logger.error(f"Unknown data type: {data_type}")
                return None

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON data: {data}, error: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error generating request for {data}: {e}")
            return None

    def start_requests(self):
        """
        单股票模式的请求生成（向后兼容）
        当直接指定code参数时使用
        """
        if not self.code:
            # Redis分布式模式，不需要生成请求
            return

        self.logger.info(f"Generating requests for single stock: {self.code}")

        cookies_xueqiu = get_xueqiu_cookie()
        symbol = stock_code(self.code)
        timestamp = str(ago_day_timestamp(0))

        # 雪球季度盈利数据
        income_url = f'https://stock.xueqiu.com/v5/stock/finance/cn/income.json?symbol={symbol}&type=all&is_detail=true&count=9&timestamp={timestamp}'
        yield FormRequest(
            url=income_url,
            meta={'cookiejar': 1, 'code': self.code, 'data_type': 'income'},
            method="GET",
            cookies=cookies_xueqiu,
            callback=self.parse_response
        )

        # 雪球个股热门文章
        hot_url = f'https://api.xueqiu.com/query/v1/symbol/search/status.json?count=10&comment=0&symbol={symbol}&hl=0&source=all&sort=alpha&page=1&q=&type=11'
        yield FormRequest(
            url=hot_url,
            meta={'cookiejar': 1, 'code': self.code, 'data_type': 'hot'},
            method="GET",
            cookies=cookies_xueqiu,
            callback=self.parse_response
        )

        # 东方财富营收占比
        business_url = f'https://emweb.securities.eastmoney.com/PC_HSF10/BusinessAnalysis/PageAjax?code={symbol}'
        yield FormRequest(
            url=business_url,
            meta={'cookiejar': 1, 'code': self.code, 'data_type': 'business'},
            method="GET",
            callback=self.parse_response
        )

    @timing_decorator('stock_detail.parse_response')
    def parse_response(self, response):
        """统一的响应解析方法"""
        try:
            data_type = response.meta.get('data_type')

            if data_type == 'business':
                yield from self._parse_business_data(response)
            elif data_type == 'income':
                yield from self._parse_income_data(response)
            elif data_type == 'hot':
                yield from self._parse_hot_data(response)
            elif data_type == 'holder':
                yield from self._parse_holder_data(response)
            else:
                self.logger.warning(f"Unknown data type: {data_type}")

        except Exception as e:
            self.handle_error(e, response)

    def _parse_business_data(self, response):
        """解析业务分析数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'zygcfx' not in json_data:
                self.logger.warning(f"Invalid business response from {response.url}")
                return

            data = json_data.get('zygcfx', [])
            processed_data = self._process_business_analysis(data)

            if processed_data:
                item = Stock2Item()
                item['code'] = response.meta.get('code')
                item['businessAnalysis'] = json.dumps(processed_data, ensure_ascii=False)

                processed_item = self.process_item(dict(item))
                if processed_item:
                    yield processed_item

        except Exception as e:
            self.logger.error(f"Error parsing business data: {e}")

    def _process_business_analysis(self, data: list) -> list:
        """处理业务分析数据"""
        try:
            # 优先获取第二级分类数据
            filtered_data = []
            rank_count = 0

            # 第一次筛选：寻找第二级分类
            for business in data:
                if business.get('RANK') == 1:
                    rank_count += 1

                if rank_count == 2 and business.get('MAINOP_TYPE') == '2':
                    filtered_data.append(business)

            # 如果没有第二级分类，则获取第一级分类
            if not filtered_data:
                rank_count = 0
                for business in data:
                    if business.get('RANK') == 1:
                        rank_count += 1

                    if (rank_count == 1 and
                        business.get('MAINOP_TYPE') in ['1', '2']):
                        filtered_data.append(business)

            return filtered_data

        except Exception as e:
            self.logger.error(f"Error processing business analysis: {e}")
            return []
                
    def _parse_income_data(self, response):
        """解析收入数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'data' not in json_data:
                self.logger.warning(f"Invalid income response from {response.url}")
                return

            data = json_data.get('data', {})
            income_list = data.get('list', [])

            if not income_list:
                self.logger.info(f"No income data found for {response.meta.get('code')}")
                return

            # 处理收入数据
            processed_data = self._process_income_data_list(income_list)
            if processed_data:
                item = Stock2Item()
                item['code'] = response.meta.get('code')
                item['profit_time'] = processed_data['profit_time']
                item['profit'] = processed_data['profit']

                processed_item = self.process_item(dict(item))
                if processed_item:
                    yield processed_item

        except Exception as e:
            self.logger.error(f"Error parsing income data: {e}")

    def _process_income_data_list(self, income_list: list) -> dict:
        """处理收入数据列表"""
        try:
            profit_time = []
            profit = []

            for index in range(len(income_list)):
                if index == len(income_list) - 1:
                    continue  # 跳过最后一个元素

                current_data = income_list[index]
                next_data = income_list[index + 1]

                report_name = current_data.get('report_name', '')
                if not report_name:
                    continue

                profit_time.append(report_name)

                # 获取净利润数据
                current_profit = current_data.get('net_profit_atsopc', [0])[0] if current_data.get('net_profit_atsopc') else 0
                next_profit = next_data.get('net_profit_atsopc', [0])[0] if next_data.get('net_profit_atsopc') else 0

                # 计算季度利润（单位：亿元）
                if '一季报' in report_name:
                    quarterly_profit = round(current_profit / 100000000, 2)
                else:
                    if current_profit * next_profit > 0:
                        quarterly_profit = round((current_profit - next_profit) / 100000000, 2)
                    elif current_profit >= 0:
                        quarterly_profit = round((current_profit + abs(next_profit)) / 100000000, 2)
                    else:
                        quarterly_profit = -round((abs(current_profit) + next_profit) / 100000000, 2)

                profit.append(quarterly_profit)

            # 反转列表，使时间顺序正确
            profit_time.reverse()
            profit.reverse()

            # 格式化时间字符串
            formatted_time = ','.join(profit_time).replace(
                '三季报', '三季度'
            ).replace(
                '年报', '四季度'
            ).replace(
                '一季报', '一季度'
            ).replace(
                '中报', '二季度'
            )

            return {
                'profit_time': formatted_time,
                'profit': ','.join(str(x) for x in profit)
            }

        except Exception as e:
            self.logger.error(f"Error processing income data list: {e}")
            return {}

    def _parse_hot_data(self, response):
        """解析热门文章数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'list' not in json_data:
                self.logger.warning(f"Invalid hot data response from {response.url}")
                return

            report_list = json_data.get('list', [])
            if not report_list:
                self.logger.info(f"No hot reports found for {response.meta.get('code')}")
                return

            # 时间过滤器
            time_filter = self._get_time_filter()

            for report in report_list:
                if self._is_valid_hot_report(report, time_filter):
                    item = self._create_hot_media_item(report)
                    if item:
                        yield item

        except Exception as e:
            self.logger.error(f"Error parsing hot data: {e}")

    def _get_time_filter(self) -> str:
        """获取时间过滤器"""
        try:
            if self.quant and hasattr(self.quant, 'stockMediaDay'):
                return ago_day_timestr(self.quant.stockMediaDay, '%Y-%m-%d')
            else:
                return ago_day_timestr(7, '%Y-%m-%d')  # 默认7天
        except Exception as e:
            self.logger.warning(f"Failed to get time filter: {e}")
            return ago_day_timestr(7, '%Y-%m-%d')

    def _is_valid_hot_report(self, report_data: dict, time_filter: str) -> bool:
        """验证热门报告数据是否有效"""
        try:
            if not all(report_data.get(field) for field in ['id', 'title', 'created_at']):
                return False

            # 检查时间过滤
            created_at = report_data.get('created_at', 0)
            if created_at:
                create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))
                if create_time < time_filter:
                    return False

            # 检查内容质量（如果有量化配置）
            if self.quant:
                content_len = len(report_data.get('text', ''))
                title_len = len(report_data.get('title', ''))
                view_count = report_data.get('view_count', 0)

                if (hasattr(self.quant, 'stockMediaContent') and
                    content_len < self.quant.stockMediaContent):
                    return False

                if (hasattr(self.quant, 'stockMediaTitle') and
                    title_len <= self.quant.stockMediaTitle):
                    return False

                if (hasattr(self.quant, 'stockMediaView') and
                    view_count <= self.quant.stockMediaView):
                    return False

            return True
        except Exception as e:
            self.logger.warning(f"Error validating hot report: {e}")
            return False

    def _create_hot_media_item(self, report_data: dict) -> dict:
        """创建热门媒体数据项"""
        try:
            # 处理时间
            created_at = report_data.get('created_at', 0)
            create_time = time.strftime('%Y-%m-%d', time.localtime(float(created_at / 1000)))

            # 创建数据项
            item = MediaItem()
            item['mediaId'] = str(report_data.get('id', ''))
            item['source'] = 'xueqiu'
            item['title'] = clear_html(report_data.get('title', ''))
            item['content'] = report_data.get('text', '')
            item['url'] = f"https://xueqiu.com/{report_data.get('user_id', '')}/{report_data.get('id', '')}"
            item['createTime'] = create_time

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating hot media item: {e}")

    def _parse_holder_data(self, response):
        """解析股东持仓数据"""
        try:
            json_data = self.parse_json_response(response)
            if not json_data or 'data' not in json_data:
                self.logger.warning(f"Invalid holder response from {response.url}")
                return

            holder_data = json_data.get('data', [])
            code = response.meta.get('code')

            if not holder_data:
                self.logger.info(f"No holder data found for {code}")
                return

            self.logger.info(f"Found {len(holder_data)} holder records for {code}")

            # 处理每个持仓记录
            for holder in holder_data:
                item = self._create_holder_item(holder)
                if item:
                    yield item

        except Exception as e:
            self.logger.error(f"Error parsing holder data: {e}")

    def _create_holder_item(self, holder_data: dict) -> dict:
        """创建持仓数据项"""
        try:
            # 生成唯一ID
            security_code = holder_data.get('SECURITY_CODE', '')
            holder_code = holder_data.get('HOLDER_CODE', '')
            unique_id = hashlib.md5(
                (security_code + holder_code).encode("utf-8")
            ).hexdigest().lower().replace('-', '')

            # 创建数据项
            item = FundItem()
            item['id'] = unique_id
            item['code'] = security_code
            item['fundCode'] = holder_code
            item['name'] = holder_data.get('HOLDER_NAME', '')
            item['type'] = holder_data.get('ORG_TYPE', '')
            item['orgCode'] = holder_data.get('PARENT_ORG_CODE', '')
            item['orgName'] = holder_data.get('PARENT_ORG_NAME', '')
            item['amount'] = holder_data.get('HOLD_MARKET_CAP', 0)
            item['ratio'] = holder_data.get('TOTAL_SHARES_RATIO', 0)

            # 处理数据项
            processed_item = self.process_item(dict(item))
            return processed_item

        except Exception as e:
            self.logger.error(f"Error creating holder item: {e}")
            return None
            return None
