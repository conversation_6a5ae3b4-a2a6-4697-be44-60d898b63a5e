services:
  ##### 部署辅助程序 #####
  deploy-db:
    platform: linux/amd64
    image: mysql:8.0.18
    container_name: deploy-db
    restart: always
    environment:
      MYSQL_USER: miz<PERSON>_PASSWORD: mzd12345
      MYSQL_DATABASE: blackbear
      MYSQL_ROOT_PASSWORD: mzd12345
    ports:
      - '3308:3306'
    logging:
      driver: 'json-file'
      options:
        max-size: '5g'
    volumes:
      - ./deploy/deploy-db/data:/var/lib/mysql
      # 挂载配置文件目录
      - ./deploy/deploy-db/conf.d:/etc/mysql/conf.d
      # - ./deploy/deploy-db/init.d:/docker-entrypoint-initdb.d
    command:
      --default-authentication-plugin=mysql_native_password
      --character-set-server=utf8mb4
      --collation-server=utf8mb4_general_ci
      --explicit_defaults_for_timestamp=true
      --lower_case_table_names=1
      --sql_mode=STRICT_TRANS_TABLES,NO_ENGINE_SUBSTITUTION
    networks:
      app-network:
        ipv4_address: ************00
  deploy-redis:
    image: redis
    container_name: deploy-redis
    command: redis-server --appendonly yes --requirepass 12345
    restart: always
    ports:
      - "6380:6379"
    volumes:
      - ./deploy/deploy-redis:/data
    networks:
      app-network:
        ipv4_address: ************01
  deploy-cron:
    platform: linux/amd64
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/deploy-cron
    container_name: deploy-cron
    restart: always
    ports:
      - "5920:5920"
    env_file:
      - ./prod.env
      - ./common.env
    environment:
      # 自动配置选项
      AUTO_CONFIG: "true"
      WAIT_FOR_DB: "true"
      GOCRON_SKIP_INSTALL_CHECK: "true"
      DB_PREFIX: "cron_"
      # 应用配置
      APP_NAME: "Blackbear定时任务管理系统"
      LOG_LEVEL: "${BB_LOG_LEVEL:-INFO}"

      # 可选的额外配置
      ENABLE_TLS: "${BB_CRON_ENABLE_TLS:-false}"
      CONCURRENCY_QUEUE: "${BB_CRON_CONCURRENCY_QUEUE:-500}"
    volumes:
      # 配置文件持久化
      - ./deploy/deploy-cron/conf:/app/conf
    depends_on:
      - deploy-db
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5920/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"
    networks:
      app-network:
        ipv4_address: ************02
    labels:
      - "com.blackbear.service=gocron"
      - "com.blackbear.description=定时任务管理系统"
  # Blackbear 多维度代理池服务 (替换原jhao104版本)
  deploy-proxy:
    platform: linux/amd64
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/proxy-pool:latest
    container_name: deploy-proxy
    restart: always
    ports:
      - "5010:5010"
    env_file:
      - ./prod.env
      - ./common.env
    volumes:
      - ./deploy/deploy-proxy/logs:/app/logs
      - ./deploy/deploy-proxy/data:/app/data
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5010/count/"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      app-network:
        ipv4_address: ************03
  ###### 爬虫 Master 节点 (数据更新 + 任务执行) #####
  crawl:
    platform: linux/amd64
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/crawl
    container_name: crawl
    restart: always
    command: ["master"]  # 使用旧版本支持的master模式
    ports:
      - "6800:6800"  # Scrapyd 端口
    environment:
      # 分布式爬虫配置 - Master 节点 (修复数据更新问题)
      CRAWLER_ROLE: "master"
      CRAWLER_MODE: "config"  # 使用配置调度模式
      CRAWLER_MACHINE_ID: "docker-master-001"
      # Master 节点调度配置 - 优化频率减少资源消耗
      CRAWLER_FREQUENT_MINUTES: "60"      # 频繁更新间隔（1小时，原30分钟）
      CRAWLER_PERIODIC_MINUTES: "720"     # 周期更新间隔（12小时，原6小时）
      CRAWLER_MAINTENANCE_MINUTES: "240"  # 维护检查间隔（4小时，原2小时）
      CRAWLER_REFRESH_MINUTES: "1440"     # 数据刷新间隔（24小时，原12小时）
      CRAWLER_CLEANUP_MINUTES: "960"      # 数据清理间隔（16小时，原8小时）
      # Redis 数据管理配置
      CRAWLER_MAX_QUEUE_SIZE: "18000"
      CRAWLER_CLEANUP_THRESHOLD: "30000"
      CRAWLER_KEEP_RECENT_COUNT: "15000"
      # 代理配置
      BB_PROXY_URL: http://deploy-proxy:5010
      PROXY_QUALITY_API: http://deploy-proxy:5010
    env_file:
      - ./prod.env
      - ./common.env
    depends_on:
      - deploy-proxy
      - deploy-redis
    networks:
      app-network:
        ipv4_address: ************07
  ###### 量化脚本 #####
  quant:
    platform: linux/amd64
    image: registry.cn-hangzhou.aliyuncs.com/blackbear/quant
    container_name: quant
    restart: always
    ports:
      - "5921:5921"
    env_file:
      - ./prod.env
      - ./common.env
    # depends_on:
    #   - deploy-db
    #   - deploy-redis
    networks:
      app-network:
        ipv4_address: ************09

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: ************/24
          gateway: ************